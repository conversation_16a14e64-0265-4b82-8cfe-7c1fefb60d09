import csv
import logging
import os
import random
import time
import pyperclip
from PySide6.QtCore import QObject, Signal
from pywinauto import mouse, Desktop

from wechat.wechat_ui_api import open_dyh_dialog_window, Independent_window, WechatTools
# 导入下载器（延迟导入避免循环依赖）
from core.official_down import WeChatArticleCrawler
crawler = WeChatArticleCrawler()
# 创建下载器实例
class OfficialProcess(QObject):
    log_message = Signal(str, str)  # 消息, 级别
    WECHAT_PATH = None
    # 全文下载配置
    ENABLE_FULL_TEXT_DOWNLOAD = True  # 是否启用全文下载
    DOWNLOAD_FORMAT = "MD + HTML"  # 下载格式: "MD + HTML", "仅MD", "仅HTML"
    DOWNLOAD_DELAY = 2  # 下载延迟（秒）
    STOP_REQUESTED = False

    def __init__(self):
        super().__init__()

    def request_stop(self):
        """请求停止当前操作"""
        self.STOP_REQUESTED = True
        self.log_message.emit("收到停止关注请求", "INFO")

    def reset_stop_flag(self):
        """重置停止标志"""
        self.STOP_REQUESTED = False

    def is_stop_requested(self):
        """检查是否请求停止"""
        return self.STOP_REQUESTED

    def download_article_sync(self, url, title, official_account):
        """同步下载单篇文章全文"""
        try:
            self.log_message.emit(f"开始下载文章全文: {title}", "INFO")
            # 下载文章内容并直接保存到downloads目录
            content_result = crawler.process_article(url, official_account=official_account, save_to_downloads=True)
            self.log_message.emit(f"文章下载结果：{title} - {content_result}", "INFO")
            if content_result and content_result.get('success'):
                self.log_message.emit(f"✓ 文章下载成功: {title}", "INFO")
                return True
            else:
                self.log_message.emit(f"✗ 文章下载失败: {title} - 内容获取失败", "ERROR")
                return False
        except Exception as e:
            self.log_message.emit(f"✗ 文章下载异常: {title} - {str(e)}", "ERROR")
            logging.exception(f"✗ 文章下载异常: {title} - {str(e)}")
            return False

    def add_to_download_queue(self, url, title, official_account):
        """添加文章到下载队列"""
        if not self.ENABLE_FULL_TEXT_DOWNLOAD:
            return
        self.log_message.emit(f"全文已添加到下载队列: {title}", "INFO")
        self.download_article_sync(url, title, official_account)

    def set_download_config(self, enable=True, format_type="MD + HTML", delay=2):
        """设置全文下载配置"""
        self.ENABLE_FULL_TEXT_DOWNLOAD = enable
        self.DOWNLOAD_FORMAT = format_type
        self.DOWNLOAD_DELAY = delay
        self.log_message.emit(f"全文下载配置已更新: 启用状态={enable}, 格式={format_type}, 延迟={delay}秒", "INFO")

    # 方法：检查控件是否在窗口内可见（忽略任务栏）
    # 部分元素获取后通过is_visible判断是否在窗口内可见时，会出现错误。
    def check_control_visibility(self, window, target_control):
        target_rect = target_control.rectangle()
        window_rect = window.rectangle()
        is_visible = (target_rect.top >= window_rect.top and
                      target_rect.bottom <= window_rect.bottom)
        return is_visible

    # 搜索公众号
    def search_official_account(self, official_window, official_name):
        # 定义日志函数
        found = False
        scroll_attempts = 0
        official_items = official_window.descendants(control_type="ListItem")
        official_items_visible_ = [item for item in official_items if self.check_control_visibility(official_window, item)]
        official_items_visible_[0].click_input()
        time.sleep(random.randint(1, 3))
        # 发送 Home 键
        official_window.type_keys("{HOME}")
        self.log_message.emit(f'返回订阅号窗口顶部', "INFO")
        while not found:
            # 检查是否请求停止
            if self.is_stop_requested():
                self.log_message.emit("检测到停止请求，退出搜索", "INFO")
                return False

            # 检查是否滑动到窗口底部
            official_items = official_window.descendants(control_type="ListItem")
            if len(official_items) == 0:
                self.log_message.emit(f'订阅号内无任何公众号', "WARNING")
                break

            official_items = official_window.descendants(control_type="ListItem")
            official_items_visible_ = [item for item in official_items if
                                       self.check_control_visibility(official_window, item)]
            time.sleep(1)
            # 在循环中也检查停止请求
            for item in official_items_visible_:
                if self.is_stop_requested():
                    self.log_message.emit("检测到停止请求，退出搜索", "INFO")
                    return False
                if item.element_info.name == official_name:
                    found = True
                    item.click_input()
                    self.log_message.emit(f'已找到同名公众号：{official_name}', "INFO")
                    break
            if not found:
                if official_items_visible_[-1].element_info.name == official_items[-1].element_info.name:
                    self.log_message.emit(f'已滑动至订阅号窗口底部', "WARNING")
                    break
                official_window.wheel_mouse_input(wheel_dist=-5)
                self.log_message.emit(f'滑动订阅号列表中......', "INFO")
                time.sleep(1)
                scroll_attempts += 1
        return found

    # 采集列表文章
    def crawl_official_detail(self, main_window, official_account, official_name, limit_count, stop_exist_count):
        desktop = Desktop(**Independent_window.Desktop)

        def reload_message_list():
            info_text = main_window.child_window(title=official_name, control_type="Text", found_index=1)
            time.sleep(random.randint(1, 3))  # 等待翻页后的渲染时间
            detail_items = (info_text.parent().parent().parent().parent().parent().parent()
                            .descendants(control_type="Pane"))
            detail_items_visibility = [i for i in detail_items if i.window_text() != '']
            return info_text, detail_items, detail_items_visibility

        info_text, detail_items, detail_items_visibility = reload_message_list()
        # 循环公众号消息列表
        self.log_message.emit(f'循环公众号消息列表', "INFO")
        cache_num = len(detail_items_visibility)
        self.log_message.emit(f'获取消息列表中数据：{cache_num}', "INFO")
        if not cache_num:
            self.log_message.emit(f"公众号{official_account}， {official_name=}，采集结束 [0/{cache_num}] {limit_count=}", "INFO")
            return
        first_detail = detail_items_visibility[0]
        while not self.check_control_visibility(main_window, first_detail):
            visibility_detail = next(
                (item for item in detail_items_visibility if self.check_control_visibility(main_window, item)), None)
            first_detail_rect = visibility_detail.rectangle()
            # 计算随机点击位置（模拟人为操作）
            padding = 3  # 留出边缘空间
            # 计算中心点
            last_center_x = (first_detail_rect.left + first_detail_rect.right) // 2 - padding
            last_center_y = (first_detail_rect.top + first_detail_rect.bottom) // 2 - padding
            time.sleep(random.randint(1, 3))  # 等待翻页后的渲染时间
            self.log_message.emit(f'滚动到最新消息')
            mouse.scroll(coords=(last_center_x, last_center_y), wheel_dist=random.randint(15, 17))
        # 重复队列
        # 读取CSV文件，提取已存在的文章名称
        csv_file_path = f"data/all.csv"
        dup_list = []
        if os.path.isfile(csv_file_path):
            with open(csv_file_path, mode='r', encoding='utf-8') as file:
                reader = csv.reader(file)
                dup_list = [row[-1] for row in reader]  # 假设第二列为文章唯一标识
        get_num = 0
        new_num = 0
        first_get = True
        _exist_count = 0

        def scroll2next(next_index):
            nonlocal info_text, detail_items, detail_items_visibility, cache_num, get_num
            old_num = len(detail_items_visibility)
            last_detail = detail_items_visibility[next_index - 1]
            # 如果元素为
            last_detail_rect = last_detail.rectangle()
            # 计算随机点击位置（模拟人为操作）
            padding = 3  # 留出边缘空间
            # 计算中心点
            last_center_x = (last_detail_rect.left + last_detail_rect.right) // 2 - padding
            last_center_y = (last_detail_rect.top + last_detail_rect.bottom) // 2 - padding
            time.sleep(random.randint(1, 3))  # 等待翻页后的渲染时间
            mouse.scroll(coords=(last_center_x, last_center_y), wheel_dist=-1)
            info_text, detail_items, detail_items_visibility = reload_message_list()
            cache_num = len(detail_items_visibility)
            cache_change = old_num != len(detail_items_visibility)
            self.log_message.emit(f'[{get_num}/{cache_num}] {cache_change=} 根据消息组件高度动态滚动，确保组件完全展示')
            return cache_change

        def check_need_crawl():
            return limit_count > get_num and stop_exist_count > _exist_count

        while check_need_crawl():

            info_text, detail_items, detail_items_visibility = reload_message_list()

            if not first_get and len(detail_items_visibility) == cache_num:
                self.log_message.emit(f'消息列表无新数据')
                break
            first_get = False
            cache_num = len(detail_items_visibility)
            while check_need_crawl() and get_num < len(detail_items_visibility):
                # 检查是否请求停止
                if self.is_stop_requested():
                    self.log_message.emit("检测到停止请求，退出采集", "INFO")
                    return

                i = get_num
                try:
                    detail = detail_items_visibility[i]
                    if not detail or not hasattr(detail, 'element_info'):
                        self.log_message.emit(f'[{get_num}/{cache_num}] 无效的详情元素，跳过', 'WARNING')
                        get_num += 1
                        continue

                    detail_name = detail.element_info.name
                    time.sleep(random.randint(1, 3))

                    # 安全地检查控件可见性
                    scroll_attempts = 0
                    while not self.check_control_visibility(main_window, detail) and scroll_attempts < 10:
                        if self.is_stop_requested():
                            self.log_message.emit("检测到停止请求，退出滚动", "INFO")
                            return
                        scroll2next(i)
                        scroll_attempts += 1

                    if scroll_attempts >= 10:
                        self.log_message.emit(f'[{get_num}/{cache_num}] 无法使元素可见，跳过', 'WARNING')
                        get_num += 1
                        continue

                    get_num += 1

                except Exception as e:
                    self.log_message.emit(f'[{get_num}/{cache_num}] 处理详情元素时出错: {str(e)}', 'ERROR')
                    get_num += 1
                    continue
                # 如果元素为
                detail_rect = detail.rectangle()
                # 计算随机点击位置（模拟人为操作）
                padding = 3  # 留出边缘空间
                # 计算中心点
                center_x = (detail_rect.left + detail_rect.right) // 2 - padding
                center_y = (detail_rect.top + detail_rect.bottom) // 2 - padding
                # 移动鼠标到按钮中心
                self.log_message.emit(f'[{get_num}/{cache_num}] 移动鼠标到消息组件中心')
                article_link = ''
                link_retry = 0

                while not article_link.startswith('https://mp.weixin.qq.com/s/') and link_retry < 10:
                    # 检查是否请求停止
                    if self.is_stop_requested():
                        self.log_message.emit("检测到停止请求，退出链接获取", "INFO")
                        return

                    link_retry += 1
                    try:
                        time.sleep(round(random.uniform(1, 3), 2))
                        mouse.move(coords=(center_x, center_y))
                        self.log_message.emit(f'[{get_num}/{cache_num}] {link_retry=} 打开详情页')
                        detail.click_input()
                        time.sleep(round(random.uniform(1, 3), 2))

                        # 安全地获取浏览器窗口
                        try:
                            detail_browser = desktop.window(
                                **{'title': '微信', 'class_name': 'Chrome_WidgetWin_0', 'control_type': 'Pane'})
                            if not detail_browser.exists():
                                self.log_message.emit(f'[{get_num}/{cache_num}] 浏览器窗口不存在，重试', 'WARNING')
                                continue

                            more_item = detail_browser.child_window(title="更多")
                            if not more_item.exists():
                                self.log_message.emit(f'[{get_num}/{cache_num}] 更多按钮不存在，重试', 'WARNING')
                                continue

                            more_item.click_input()
                            time.sleep(round(random.uniform(1, 3), 2))

                            link_item = detail_browser.child_window(title="复制链接", control_type="MenuItem")
                            if not link_item.exists():
                                self.log_message.emit(f'[{get_num}/{cache_num}] 复制链接按钮不存在，重试', 'WARNING')
                                continue

                            link_item.click_input()
                            time.sleep(round(random.uniform(1, 3), 2))

                            # 获取剪切板文本
                            self.log_message.emit(f'[{get_num}/{cache_num}] {link_retry=} 获取剪切板复制链接')
                            article_link = pyperclip.paste()

                        except Exception as e:
                            self.log_message.emit(f'[{get_num}/{cache_num}] 获取链接时出错: {str(e)}', 'ERROR')
                            continue

                    except Exception as e:
                        logging.exception("获取链接时出错")
                        self.log_message.emit(f'[{get_num}/{cache_num}] 点击操作时出错: {str(e)}', 'ERROR')
                        continue
                    except Exception as e:
                        self.log_message.emit(f'[{get_num}/{cache_num}] {link_retry=} 获取剪切板复制链接失败 {detail_name=}',
                                 'ERROR')
                        article_link = ''
                        more_item.click_input()
                        time.sleep(round(random.uniform(1, 3), 2))
                        break
                    finally:
                        detail_browser.close()
                        self.log_message.emit(f'[{get_num}/{cache_num}] {link_retry=} 关闭详情页')

                # 在处理链接前检查停止请求
                if self.is_stop_requested():
                    self.log_message.emit("检测到停止请求，退出采集", "INFO")
                    return

                if not article_link:
                    self.log_message.emit(f'跳过异常链接 [{get_num}/{cache_num}] {detail_name=}')
                    continue
                if article_link in dup_list:
                    self.log_message.emit(f'跳过重复内容 [{get_num}/{cache_num}] {_exist_count=} {stop_exist_count=} {detail_name=}')
                    _exist_count += 1
                    continue
                _exist_count = 0
                dup_list.append(article_link)
                # 保存数据到CSV文件
                csv_file_path = f"data/{official_account}.csv"
                file_exists = os.path.isfile(csv_file_path)
                # 同步下载文章全文
                if self.ENABLE_FULL_TEXT_DOWNLOAD and article_link:
                    # 在下载前再次检查停止请求
                    if self.is_stop_requested():
                        self.log_message.emit("检测到停止请求，跳过下载任务", "INFO")
                        return
                    try:
                        self.add_to_download_queue(article_link, detail_name, official_account)
                        self.log_message.emit(f"已提交全文下载任务: {detail_name}", "INFO")
                    except Exception as e:
                        self.log_message.emit(f"提交下载任务失败: {detail_name} - {str(e)}", "ERROR")
                self.log_message.emit(f'[{get_num}/{cache_num}] 已将文章链接保存至结果CSV：{article_link}')
                with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    if not file_exists:
                        writer.writerow(['标题', '全文链接'])  # 写入表头
                    writer.writerow([detail_name, article_link])  # 追加数据
                csv_file_path = f"data/all.csv"
                file_exists = os.path.isfile(csv_file_path)
                with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    if not file_exists:
                        writer.writerow(['账号', '名称', '全文链接'])  # 写入表头
                    writer.writerow([official_account, official_name, article_link])  # 追加数据
                self.log_message.emit(f'[{get_num}/{cache_num}] 已将文章链接保存至全部去重CSV：{article_link}')

                # self.log_message.emit(f'{detail_name}:{clipboard_text}')
                self.log_message.emit(f'[{get_num}/{cache_num}] {detail_name}:{article_link}')
                time.sleep(random.randint(1, 3))  # 等待弹窗加载'

                new_num += 1
            if check_need_crawl():
                # 用于加载下一页
                page_change = False
                page_change_retry = 15
                while page_change_retry > 0 and not page_change:
                    page_change = scroll2next(get_num)
                    page_change_retry -= 1
                self.log_message.emit(f"翻页结果 [{get_num}/{cache_num}] {page_change=} {page_change_retry=}")
        self.log_message.emit(f"公众号采集结束 [{get_num}/{cache_num}] {limit_count=} {new_num=} {official_name=}")

    # 列表数据采集
    def official_process(self, official_infos, limit_count=3, stop_exist_count=30):

        self.log_message.emit(f"公众号列表数据采集开始", 'INFO')

        # 重置停止标志
        self.reset_stop_flag()

        for official in official_infos:
            # 检查是否请求停止
            if self.is_stop_requested():
                self.log_message.emit("检测到停止请求，退出采集", "INFO")
                return
            try:
                account_ = official['official_account']
                name_ = official['official_name']
                self.log_message.emit(f'自动化公众号采集程序开始执行，账号：{account_}，名称：{name_}')
                self.log_message.emit(f'最多获取消息数量：{limit_count}')

                # 安全地打开微信窗口
                official_window = None
                main_window = None

                try:
                    official_window, main_window = open_dyh_dialog_window(
                        '订阅号',
                        search_pages=0,
                        wechat_path=self.WECHAT_PATH,
                        is_maximize=False
                    )

                    if not official_window or not main_window:
                        self.log_message.emit(f'无法打开微信窗口，跳过账号：{account_}', 'ERROR')
                        continue

                    # 检查停止请求
                    if self.is_stop_requested():
                        self.log_message.emit("检测到停止请求，退出采集", "INFO")
                        return

                    found = self.search_official_account(official_window, name_)
                    if found:
                        self.crawl_official_detail(main_window, account_, name_, limit_count, stop_exist_count)
                    else:
                        self.log_message.emit(f'订阅号内无该公众号，账号：{account_}，名称：{name_}', 'WARNING')

                except Exception as e:
                    import traceback
                    self.log_message.emit(f'处理账号 {account_} 时出错: {traceback.format_exc()}', 'ERROR')
                    continue
                finally:
                    # 确保窗口资源被释放
                    try:
                        if official_window:
                            official_window.close()
                    except:
                        pass
                    try:
                        if main_window:
                            main_window.close()
                    except:
                        pass
            except Exception as e:
                self.log_message.emit(f'采集过程中出现异常: {str(e)}', 'ERROR')
                continue

    # 关注公众号
    def follow_official(self, accounts):
        self.log_message.emit("开始关注公众号", "INFO")
        return [
            WechatTools.open_wechat_official_account_follow(
                account,
                wechat_path=self.WECHAT_PATH,
                load_delay=3,
                is_maximize=False
            )
            for account in accounts
        ]
